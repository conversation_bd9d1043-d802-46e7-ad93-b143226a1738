@model Web_DATN.ViewModels.LinhKienFullViewModel
@{
    ViewData["Title"] = "Sửa linh kiện";
}

<h2><PERSON><PERSON><PERSON></h2>

@if (!string.IsNullOrEmpty(ViewBag.Error))
{
    <div class="alert alert-danger">@ViewBag.Error</div>
}

<form asp-action="Edit" method="post" asp-controller="LinhKien">
    <input type="hidden" asp-for="LinhKienId" />
    
    <div class="mb-3">
        <label asp-for="TenLinhKien" class="form-label">Tên linh kiện</label>
        <input asp-for="TenLinhKien" class="form-control" />
        <span asp-validation-for="TenLinhKien" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="DanhMucId" class="form-label"><PERSON>h mục</label>
        <select asp-for="DanhMucId" asp-items="Model.DanhMucs" class="form-control">
            <option value="">-- <PERSON><PERSON><PERSON> danh mục --</option>
        </select>
        <span asp-validation-for="DanhMucId" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="Gia" class="form-label">Giá</label>
        <input asp-for="Gia" class="form-control" type="number" step="0.01" />
        <span asp-validation-for="Gia" class="text-danger"></span>
    </div>

    <div class="mb-3">
        <label asp-for="MoTa" class="form-label">Mô tả</label>
        <textarea asp-for="MoTa" class="form-control" rows="4"></textarea>
    </div>

    <div class="form-check mb-3">
        <input asp-for="TrangThai" class="form-check-input" />
        <label class="form-check-label" asp-for="TrangThai">Hiển thị</label>
    </div>

    <div class="mb-3">
        <button type="submit" class="btn btn-primary">Cập nhật</button>
        <a asp-action="Index" class="btn btn-secondary">Hủy</a>
    </div>
</form>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
