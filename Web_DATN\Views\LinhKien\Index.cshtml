﻿@model List<F4_API.Models.LinhKien>
@{
    ViewData["Title"] = "Danh sách linh kiện";
}

<h2><PERSON><PERSON> sách <PERSON></h2>

<a asp-action="Create" class="btn btn-success mb-3">+ Thêm linh kiện</a>

@if (!Model.Any())
{
    <div class="alert alert-info">Chưa có linh kiện nào.</div>
}
else
{
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>Tên linh kiện</th>
                <th>Danh mục</th>
                <th>Gi<PERSON></th>
                <th>Mô tả</th>
                <th>Trạng thái</th>
                <th>Thao tác</th>
            </tr>
        </thead>
        <tbody>
            @foreach (var item in Model)
            {
                <tr>
                    <td>@item.TenLinhKien</td>
                    <td>@item.DanhMuc?.TenDanhMuc</td>
                    <td>@string.Format("{0:N0} đ", item.Gia)</td>
                    <td>@(string.IsNullOrEmpty(item.MoTa) ? "Không có mô tả" : (item.MoTa.Length > 50 ? item.MoTa.Substring(0, 50) + "..." : item.MoTa))</td>
                    <td>
                        @if (item.TrangThai == true)
                        {
                            <span class="badge bg-success">Hiển thị</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Ẩn</span>
                        }
                    </td>
                    <td>
                        <a asp-action="Details" asp-route-id="@item.LkId" class="btn btn-info btn-sm">Chi tiết</a>
                        <a asp-action="Edit" asp-route-id="@item.LkId" class="btn btn-warning btn-sm">Sửa</a>
                        <a asp-action="Delete" asp-route-id="@item.LkId" class="btn btn-danger btn-sm"
                           onclick="return confirm('Bạn có chắc chắn muốn xóa linh kiện này?')">Xóa</a>
                    </td>
                </tr>
            }
        </tbody>
    </table>
}
