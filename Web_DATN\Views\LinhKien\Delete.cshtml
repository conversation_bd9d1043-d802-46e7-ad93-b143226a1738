@model F4_API.Models.LinhKien
@{
    ViewData["Title"] = "Xóa linh kiện";
}

<h2><PERSON><PERSON><PERSON></h2>

<div class="alert alert-warning">
    <h4>Bạn có chắc chắn muốn xóa linh kiện này?</h4>
    <p>Hành động này không thể hoàn tác.</p>
</div>

@if (!string.IsNullOrEmpty(ViewBag.Error))
{
    <div class="alert alert-danger">@ViewBag.Error</div>
}

<div class="card">
    <div class="card-header">
        <h4>@Model.TenLinhKien</h4>
    </div>
    <div class="card-body">
        <dl class="row">
            <dt class="col-sm-3">Tên linh kiện:</dt>
            <dd class="col-sm-9">@Model.TenLinhKien</dd>

            <dt class="col-sm-3"><PERSON><PERSON> mục:</dt>
            <dd class="col-sm-9">@Model.DanhMuc?.TenDanhMuc</dd>

            <dt class="col-sm-3">Giá:</dt>
            <dd class="col-sm-9">@string.Format("{0:N0} đ", Model.Gia)</dd>

            <dt class="col-sm-3">Mô tả:</dt>
            <dd class="col-sm-9">@(string.IsNullOrEmpty(Model.MoTa) ? "Không có mô tả" : Model.MoTa)</dd>

            <dt class="col-sm-3">Trạng thái:</dt>
            <dd class="col-sm-9">
                @if (Model.TrangThai == true)
                {
                    <span class="badge bg-success">Hiển thị</span>
                }
                else
                {
                    <span class="badge bg-secondary">Ẩn</span>
                }
            </dd>
        </dl>
    </div>
    <div class="card-footer">
        <form asp-action="Delete" method="post">
            <input type="hidden" asp-for="LkId" />
            <button type="submit" class="btn btn-danger">Xác nhận xóa</button>
            <a asp-action="Index" class="btn btn-secondary">Hủy</a>
        </form>
    </div>
</div>
