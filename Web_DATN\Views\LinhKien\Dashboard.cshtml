@{
    ViewData["Title"] = "Tổng quan sản phẩm";
}

<h2>Tổng quan Quản lý Sản phẩm</h2>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-header">
                <i class="fas fa-boxes"></i> Tổng số linh kiện
            </div>
            <div class="card-body">
                <h4 class="card-title">@ViewBag.TongSoLinhKien</h4>
                <p class="card-text">Tổng số linh kiện trong hệ thống</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-header">
                <i class="fas fa-eye"></i> Đang hiển thị
            </div>
            <div class="card-body">
                <h4 class="card-title">@ViewBag.SoLinhKienHienThi</h4>
                <p class="card-text">Linh kiện đang đư<PERSON> hiển thị</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-secondary">
            <div class="card-header">
                <i class="fas fa-eye-slash"></i> Đang ẩn
            </div>
            <div class="card-body">
                <h4 class="card-title">@ViewBag.SoLinhKienAn</h4>
                <p class="card-text">Linh kiện đang bị ẩn</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-header">
                <i class="fas fa-percentage"></i> Tỷ lệ hiển thị
            </div>
            <div class="card-body">
                <h4 class="card-title">
                    @if (ViewBag.TongSoLinhKien > 0)
                    {
                        @(Math.Round((double)ViewBag.SoLinhKienHienThi / ViewBag.TongSoLinhKien * 100, 1))%
                    }
                    else
                    {
                        @(0)%
                    }
                </h4>
                <p class="card-text">Tỷ lệ linh kiện hiển thị</p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-list"></i> Linh kiện mới nhất</h5>
            </div>
            <div class="card-body">
                @if (ViewBag.LinhKienMoiNhat != null && ((List<F4_API.Models.LinhKien>)ViewBag.LinhKienMoiNhat).Any())
                {
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Tên linh kiện</th>
                                    <th>Giá</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in (List<F4_API.Models.LinhKien>)ViewBag.LinhKienMoiNhat)
                                {
                                    <tr>
                                        <td>@item.TenLinhKien</td>
                                        <td>@string.Format("{0:N0} đ", item.Gia)</td>
                                        <td>
                                            @if (item.TrangThai == true)
                                            {
                                                <span class="badge bg-success">Hiển thị</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-secondary">Ẩn</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <p class="text-muted">Chưa có linh kiện nào.</p>
                }
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> Thao tác nhanh</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a asp-action="Create" class="btn btn-success">
                        <i class="fas fa-plus"></i> Thêm linh kiện mới
                    </a>
                    <a asp-action="Index" class="btn btn-primary">
                        <i class="fas fa-list"></i> Xem tất cả linh kiện
                    </a>
                    <hr>
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i> 
                        Sử dụng các thao tác trên để quản lý linh kiện một cách hiệu quả.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
