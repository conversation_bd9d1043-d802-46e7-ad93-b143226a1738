using F4_API.Models;
using F4_API.Models.DTO;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Newtonsoft.Json;
using System.Text;
using Web_DATN.ViewModels;

namespace Web_DATN.Controllers
{
    public class LinhKienController : Controller
    {
        private readonly HttpClient _httpClient;
        private const string BaseApiUrl = "https://localhost:7183/api/LinhKiens";
        private const string ThuocTinhApiUrl = "https://localhost:7183/api/DanhMuc_LinhKien_ThuocTinh";
        private const string DanhMucApiUrl = "https://localhost:7183/api/DanhMucs";

        public LinhKienController()
        {
            _httpClient = new HttpClient();
        }

        // Hiển thị danh sách linh kiện
        public async Task<IActionResult> Index(Guid? loaiSanPham, string TimKiem)
        {
            string url;

            if (!string.IsNullOrEmpty(TimKiem))
            {
                url = $"{BaseApiUrl}/search?keyword={Uri.EscapeDataString(TimKiem)}";
            }
            else if (loaiSanPham.HasValue)
            {
                url = $"{BaseApiUrl}/by-category/{loaiSanPham.Value}";
            }
            else
            {
                url = BaseApiUrl;
            }

            var response = await _httpClient.GetAsync(url);
            var linhKiens = new List<LinhKien>();

            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                linhKiens = JsonConvert.DeserializeObject<List<LinhKien>>(json)!;
            }

            ViewBag.TimKiem = TimKiem;
            ViewBag.LoaiSanPham = loaiSanPham;
            return View(linhKiens);
        }

        // GET: /LinhKien/Create
        [HttpGet]
        public async Task<IActionResult> Create()
        {
            var vm = new LinhKienFullViewModel
            {
                DanhMucs = await GetDanhMucSelectList(),
                ThuocTinhs = new List<LinhKienThuocTinhCreateViewModel>()
            };
            return View(vm);
        }

        // POST: /LinhKien/Create
        [HttpPost]
        public async Task<IActionResult> Create(LinhKienFullViewModel vm)
        {
            if (!ModelState.IsValid)
            {
                vm.DanhMucs = await GetDanhMucSelectList();
                return View(vm);
            }

            var dto = new LinhKienDTO
            {
                TenLinhKien = vm.TenLinhKien,
                DanhMucId = vm.DanhMucId,
                Gia = vm.Gia,
                MoTa = vm.MoTa,
                TrangThai = vm.TrangThai,
                linhKienCTs = vm.ThuocTinhs.Select(ct => new LinhKienChiTietsDTO
                {
                    ThuocTinhId = ct.ThuocTinhId,
                    GiaTri = ct.GiaTri,
                    MoTa = ct.MoTa,
                    TrangThai = ct.TrangThai
                }).ToList()
            };

            var content = new StringContent(JsonConvert.SerializeObject(dto), Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync(BaseApiUrl, content);

            if (response.IsSuccessStatusCode)
                return RedirectToAction(nameof(Index));

            ViewBag.Error = "Không thể tạo mới linh kiện.";
            vm.DanhMucs = await GetDanhMucSelectList();
            return View(vm);
        }

        // AJAX: Lấy thuộc tính theo DanhMucId
        [HttpGet]
        public async Task<IActionResult> GetThuocTinhsTheoDanhMuc(string danhMucId)
        {
            var response = await _httpClient.GetAsync($"{ThuocTinhApiUrl}/{danhMucId}");
            if (!response.IsSuccessStatusCode)
                return Json(new List<object>());

            var json = await response.Content.ReadAsStringAsync();
            return Content(json, "application/json");
        }

        // Helper: load dropdown DanhMuc
        private async Task<List<SelectListItem>> GetDanhMucSelectList()
        {
            var response = await _httpClient.GetAsync(DanhMucApiUrl);
            var list = new List<SelectListItem>();
            if (response.IsSuccessStatusCode)
            {
                var json = await response.Content.ReadAsStringAsync();
                var data = JsonConvert.DeserializeObject<List<DanhMuc>>(json)!;
                list = data.Select(dm => new SelectListItem
                {
                    Value = dm.DanhMucId.ToString(),
                    Text = dm.TenDanhMuc
                }).ToList();
            }
            return list;
        }

        // GET: /LinhKien/Details/5
        public async Task<IActionResult> Details(Guid id)
        {
            var response = await _httpClient.GetAsync($"{BaseApiUrl}/{id}");
            if (!response.IsSuccessStatusCode)
                return NotFound();

            var json = await response.Content.ReadAsStringAsync();
            var linhKien = JsonConvert.DeserializeObject<LinhKien>(json);
            return View(linhKien);
        }

        // GET: /LinhKien/Edit/5
        public async Task<IActionResult> Edit(Guid id)
        {
            var response = await _httpClient.GetAsync($"{BaseApiUrl}/{id}");
            if (!response.IsSuccessStatusCode)
                return NotFound();

            var json = await response.Content.ReadAsStringAsync();
            var linhKien = JsonConvert.DeserializeObject<LinhKien>(json);

            var vm = new LinhKienFullViewModel
            {
                LinhKienId = linhKien.LkId,
                TenLinhKien = linhKien.TenLinhKien,
                DanhMucId = linhKien.DanhMucId ?? Guid.Empty,
                Gia = linhKien.Gia ?? 0,
                MoTa = linhKien.MoTa,
                TrangThai = linhKien.TrangThai ?? true,
                DanhMucs = await GetDanhMucSelectList()
            };

            return View(vm);
        }

        // POST: /LinhKien/Edit/5
        [HttpPost]
        public async Task<IActionResult> Edit(Guid id, LinhKienFullViewModel vm)
        {
            if (id != vm.LinhKienId)
                return BadRequest();

            if (!ModelState.IsValid)
            {
                vm.DanhMucs = await GetDanhMucSelectList();
                return View(vm);
            }

            var dto = new LinhKienDTO
            {
                LkId = id,
                TenLinhKien = vm.TenLinhKien,
                DanhMucId = vm.DanhMucId,
                Gia = vm.Gia,
                MoTa = vm.MoTa,
                TrangThai = vm.TrangThai
            };

            var content = new StringContent(JsonConvert.SerializeObject(dto), Encoding.UTF8, "application/json");
            var response = await _httpClient.PutAsync($"{BaseApiUrl}/{id}", content);

            if (response.IsSuccessStatusCode)
                return RedirectToAction(nameof(Index));

            ViewBag.Error = "Không thể cập nhật linh kiện.";
            vm.DanhMucs = await GetDanhMucSelectList();
            return View(vm);
        }

        // GET: /LinhKien/Delete/5
        public async Task<IActionResult> Delete(Guid id)
        {
            var response = await _httpClient.GetAsync($"{BaseApiUrl}/{id}");
            if (!response.IsSuccessStatusCode)
                return NotFound();

            var json = await response.Content.ReadAsStringAsync();
            var linhKien = JsonConvert.DeserializeObject<LinhKien>(json);
            return View(linhKien);
        }

        // POST: /LinhKien/Delete/5
        [HttpPost, ActionName("Delete")]
        public async Task<IActionResult> DeleteConfirmed(Guid id)
        {
            var response = await _httpClient.DeleteAsync($"{BaseApiUrl}/{id}");
            if (response.IsSuccessStatusCode)
                return RedirectToAction(nameof(Index));

            ViewBag.Error = "Không thể xóa linh kiện.";
            return RedirectToAction(nameof(Index));
        }

        // Dashboard - Trang tổng quan
        public async Task<IActionResult> Dashboard()
        {
            try
            {
                var response = await _httpClient.GetAsync(BaseApiUrl);
                var linhKiens = new List<LinhKien>();

                if (response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    linhKiens = JsonConvert.DeserializeObject<List<LinhKien>>(json)!;
                }

                ViewBag.TongSoLinhKien = linhKiens.Count;
                ViewBag.SoLinhKienHienThi = linhKiens.Count(x => x.TrangThai == true);
                ViewBag.SoLinhKienAn = linhKiens.Count(x => x.TrangThai == false);
                ViewBag.LinhKienMoiNhat = linhKiens.OrderByDescending(x => x.LkId).Take(5).ToList();

                return View();
            }
            catch (Exception)
            {
                ViewBag.TongSoLinhKien = 0;
                ViewBag.SoLinhKienHienThi = 0;
                ViewBag.SoLinhKienAn = 0;
                ViewBag.LinhKienMoiNhat = new List<LinhKien>();
                return View();
            }
        }
    }
}
