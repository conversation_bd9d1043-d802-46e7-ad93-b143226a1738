@model F4_API.Models.LinhKien
@{
    ViewData["Title"] = "Chi tiết linh kiện";
}

<h2>Chi tiết <PERSON></h2>

<div class="card">
    <div class="card-header">
        <h4>@Model.TenLinhKien</h4>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <dl class="row">
                    <dt class="col-sm-4">Tên linh kiện:</dt>
                    <dd class="col-sm-8">@Model.TenLinhKien</dd>

                    <dt class="col-sm-4">Danh mục:</dt>
                    <dd class="col-sm-8">@Model.DanhMuc?.TenDanhMuc</dd>

                    <dt class="col-sm-4">Giá:</dt>
                    <dd class="col-sm-8">@string.Format("{0:N0} đ", Model.Gia)</dd>

                    <dt class="col-sm-4">Trạng thái:</dt>
                    <dd class="col-sm-8">
                        @if (Model.TrangThai == true)
                        {
                            <span class="badge bg-success">Hiển thị</span>
                        }
                        else
                        {
                            <span class="badge bg-secondary">Ẩn</span>
                        }
                    </dd>
                </dl>
            </div>
            <div class="col-md-6">
                <h5>Mô tả:</h5>
                <p>@(string.IsNullOrEmpty(Model.MoTa) ? "Không có mô tả" : Model.MoTa)</p>
            </div>
        </div>

        @if (Model.ChiTiets != null && Model.ChiTiets.Any())
        {
            <hr />
            <h5>Chi tiết thuộc tính:</h5>
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead>
                        <tr>
                            <th>Thuộc tính</th>
                            <th>Giá trị</th>
                            <th>Mô tả</th>
                            <th>Trạng thái</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var ct in Model.ChiTiets)
                        {
                            <tr>
                                <td>Thuộc tính @ct.ThuocTinhId</td>
                                <td>@ct.GiaTri</td>
                                <td>@ct.MoTa</td>
                                <td>
                                    @if (ct.TrangThai == true)
                                    {
                                        <span class="badge bg-success">Hiển thị</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-secondary">Ẩn</span>
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
    </div>
    <div class="card-footer">
        <a asp-action="Edit" asp-route-id="@Model.LkId" class="btn btn-warning">Sửa</a>
        <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách</a>
    </div>
</div>
